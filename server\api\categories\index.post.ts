import { createError } from "h3";
import { collection, addDoc, doc, getDoc } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { Category, formatCategoryData } from "~/types/categories";

const CATEGORIES_COLLECTION = "categories";

/**
 * Rota para criar uma nova categoria
 * POST /api/categories
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter os dados da requisição
    const body = await readBody(event);

    if (!body.name) {
      throw createError({
        statusCode: 400,
        statusMessage: "Nome da categoria é obrigatório",
      });
    }

    console.log("🔍 [API] Criando nova categoria:", body.name);

    // Dados a serem salvos
    const categoryData = {
      name: body.name,
      fatherId: body.fatherId || null,
      description: body.description || "",
      createdAt: new Date().toISOString(),
    };

    // Criar a categoria
    const categoriesRef = collection(db, CATEGORIES_COLLECTION);
    const docRef = await addDoc(categoriesRef, categoryData);

    // Obter a categoria criada
    const categoryDocRef = doc(db, CATEGORIES_COLLECTION, docRef.id);
    const categoryDoc = await getDoc(categoryDocRef);
    const data = categoryDoc.data();

    // Formatar a resposta
    const category = {
      id: categoryDoc.id,
      name: data?.name || "",
      fatherId: data?.fatherId || null,
      description: data?.description || "",
      createdAt: data?.createdAt || new Date().toISOString(),
    };

    console.log(`u2705 [API] Categoria ${category.name} criada com sucesso`);

    return category;
  } catch (error: any) {
    console.error("u274c [API] Erro ao criar categoria:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "Erro ao criar categoria",
    });
  }
});
