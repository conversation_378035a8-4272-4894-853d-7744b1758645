<template>
  <div>
    <main class="min-h-screen bg-gray-50">
      <div class="container mx-auto px-4 py-8">
        <NuxtLink
          v-if="route.query.fromCategory"
          :to="'/categorias?category=' + route.query.fromCategory"
          class="inline-block mb-6 text-red-600 hover:text-red-700"
        >
          ← Voltar para Categoria
        </NuxtLink>
        <div
          v-if="figure"
          class="bg-white rounded-lg shadow-lg overflow-hidden"
        >
          <!-- Gallery and Info Grid -->
          <div class="grid md:grid-cols-2 gap-8 p-6">
            <!-- Gallery Section -->
            <div class="space-y-4">
              <!-- Main Image -->
              <div class="aspect-[4/5] bg-gray-100 rounded-lg overflow-hidden">
                <template v-if="isVideo(currentImage)">
                  <div class="relative w-full h-full">
                    <video
                      class="absolute inset-0 w-full h-full object-contain bg-black"
                      controls
                    >
                      <source :src="currentImage" type="video/mp4" />
                      Seu navegador não suporta vídeos.
                    </video>
                  </div>
                </template>
                <template v-else>
                  <nuxt-img
                    :src="currentImage"
                    :alt="figure.value?.alias || 'Imagem do produto'"
                    class="w-full h-full object-contain"
                    loading="eager"
                    :preload="true"
                    format="webp"
                    quality="90"
                    :width="800"
                    :height="1000"
                    sizes="(max-width: 768px) 100vw, 50vw"
                    placeholder
                  />
                </template>
              </div>

              <!-- Thumbnail Gallery -->
              <div class="grid grid-cols-4 gap-2">
                <button
                  v-for="(media, index) in allMedia"
                  :key="index"
                  @click="currentImageIndex = index"
                  class="aspect-square bg-gray-100 rounded-lg overflow-hidden hover:ring-2 hover:ring-red-600 transition-all relative"
                  :class="{
                    'ring-2 ring-red-600': currentImageIndex === index,
                  }"
                >
                  <template v-if="isVideo(media)">
                    <div
                      class="absolute inset-0 bg-black/10 flex items-center justify-center z-10"
                    >
                      <i class="fas fa-play text-white text-2xl"></i>
                    </div>
                    <video
                      class="w-full h-full object-cover"
                      muted
                      playsinline
                      preload="metadata"
                      @loadedmetadata="
                        handleVideoLoad(
                          $event.target as HTMLVideoElement,
                          media
                        )
                      "
                      :ref="(el: any) => { if (el) thumbnailVideos[media] = el as HTMLVideoElement }"
                    >
                      <source :src="media" type="video/mp4" />
                    </video>
                  </template>
                  <template v-else>
                    <nuxt-img
                      :src="media"
                      :alt="`${figure.value?.alias || 'Produto'} - Mídia ${
                        index + 1
                      }`"
                      class="w-full h-full object-contain"
                      loading="lazy"
                      format="webp"
                      quality="85"
                      :width="200"
                      :height="200"
                      sizes="(max-width: 768px) 25vw, 10vw"
                    />
                  </template>
                </button>
              </div>
            </div>
            <!-- Info Section -->
            <div class="space-y-6">
              <div class="flex justify-between items-start">
                <div>
                  <h1 class="text-3xl font-bold text-gray-900">
                    {{ figure.value?.alias || "Produto não encontrado" }}
                  </h1>
                </div>
                <FavoriteButton
                  v-if="figure.value?.id"
                  :figure-id="figure.value.id"
                  @require-login="handleRequireLogin('favorite')"
                  class="bg-gray-100 hover:bg-gray-200 rounded-full p-2 transition-colors"
                />
              </div>

              <!-- Preço e Desconto -->
              <div v-if="figure.value" class="space-y-2">
                <div class="mt-2 space-y-1">
                  <!-- Debug: discount_percentage = {{ discount_percentage }}, totalDiscount = {{ figure.value?.totalDiscount }} -->
                  <template v-if="discount_percentage > 0">
                    <p class="text-lg text-gray-500 line-through">
                      {{ figure.value.formattedWithoutDiscount }}
                    </p>
                    <p class="text-3xl font-bold text-red-600">
                      {{ figure.value.formattedPrice }}
                    </p>
                    <p class="text-sm text-green-600 font-medium">
                      Economia de {{ discount_percentage }}%
                    </p>
                    <div
                      v-if="!figure.value?.previously_produced"
                      class="mt-2 px-2 py-1 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-700 inline-flex items-center"
                    >
                      <span class="mr-1">💥</span> Preço promocional de
                      pré-lançamento
                    </div>
                  </template>
                  <template v-else>
                    <p class="text-3xl font-bold text-gray-900">
                      {{ figure.value.formattedPrice }}
                    </p>
                  </template>
                </div>

                <!-- Frete Grátis -->
                <div class="mt-4 flex items-center space-x-2 text-green-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span class="font-medium">Frete Grátis para todo Brasil</span>
                </div>
              </div>

              <div class="space-y-2">
                <span
                  v-if="figure.value?.colored"
                  class="p-2 bg-blue-100 text-blue-800 text-sm font-medium rounded block w-full"
                  >PRODUTO VENDIDO EM CORES!</span
                >
                <span
                  v-if="figure.value?.previously_produced"
                  class="p-2 bg-purple-100 text-purple-800 text-sm font-medium rounded block w-full"
                  >PRODUTO JÁ PRODUZIDO ANTERIORMENTE!</span
                >
              </div>

              <!-- Informações de Envio -->
              <div v-if="figure.value" class="mt-6 p-4 bg-gray-50 rounded-lg">
                <div class="flex items-start space-x-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-gray-600 mt-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0"
                    />
                  </svg>
                  <div>
                    <h3 class="font-medium text-gray-900">
                      Informações de Envio
                    </h3>
                    <ul class="mt-2 space-y-2 text-sm text-gray-600">
                      <li class="flex items-center">
                        <svg
                          class="h-4 w-4 mr-2 text-green-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        Frete Grátis para todo o Brasil
                      </li>
                      <li class="flex items-center">
                        <svg
                          class="h-4 w-4 mr-2 text-blue-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        {{ formatProductionMessage }}
                      </li>
                      <li class="flex items-center mt-2">
                        <svg
                          class="h-4 w-4 mr-2 text-green-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                        <span class="font-medium text-green-700">{{
                          formatDeliveryDate
                        }}</span>
                      </li>
                      <li class="flex items-center">
                        <svg
                          class="h-4 w-4 mr-2 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                          />
                        </svg>
                        Embalagem reforçada e segura
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- Descrição do Produto -->
              <div
                v-if="figure.value"
                class="mt-6 p-4 bg-white border border-gray-200 rounded-lg"
              >
                <div class="flex items-start space-x-3 mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-gray-600 mt-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <h3 class="font-medium text-gray-900">Sobre o Produto</h3>
                </div>
                <div
                  v-html="
                    figure.value?.description ??
                    (figure.value?.previously_produced
                      ? description_2
                      : description_1)
                  "
                  class="prose max-w-none text-gray-700"
                ></div>
              </div>

              <!-- Características -->
              <div v-if="figure.value" class="space-y-4">
                <h2 class="text-xl font-semibold text-gray-900">
                  Características
                </h2>
                <ul class="space-y-3">
                  <li
                    v-if="figure.value.characteristics?.weight"
                    class="flex items-center gap-2"
                  >
                    <i class="fas fa-weight text-gray-400"></i>
                    <span
                      >Peso:
                      {{
                        formatMeasure(
                          figure.value.characteristics.weight,
                          "weight"
                        )
                      }}</span
                    >
                  </li>
                  <li
                    v-if="figure.value.characteristics?.height"
                    class="flex items-center gap-2"
                  >
                    <i class="fas fa-ruler-vertical text-gray-400"></i>
                    <span
                      >Altura:
                      {{
                        formatMeasure(
                          figure.value.characteristics.height,
                          "height"
                        )
                      }}</span
                    >
                  </li>
                </ul>
              </div>

              <!-- Link Mercado Livre -->
              <div
                v-if="figure.value?.idML && !figure.value?.site_exclusive"
                class="mt-4 p-4 bg-yellow-50 border border-yellow-100 rounded-lg"
              >
                <div class="flex items-center gap-3 mb-2">
                  <img
                    src="~/assets/images/mercado-livre-logo-8.png"
                    alt="Logo Mercado Livre"
                    class="h-6 w-auto"
                  />
                  <p class="text-sm text-gray-600">
                    Prefere comprar pelo Mercado Livre?
                  </p>
                </div>
                <template v-if="checking">
                  <div class="text-sm text-gray-500">Carregando link...</div>
                </template>
                <a
                  v-else
                  :href="
                    isLinkWorking
                      ? `https://produto.mercadolivre.com.br/${getProductId(
                          figure.value.id
                        )}`
                      : defaultMLLink
                  "
                  target="_blank"
                  rel="noopener noreferrer"
                  class="text-yellow-600 hover:text-yellow-700 font-medium flex items-center"
                >
                  Clique aqui para ver no Mercado Livre
                  <svg
                    class="w-4 h-4 ml-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                    />
                  </svg>
                </a>
              </div>
              <div
                v-else-if="figure.value?.site_exclusive"
                class="mt-4 p-4 bg-yellow-50 border border-yellow-100 rounded-lg"
              >
                <div class="flex items-center gap-3 mb-2">
                  <img
                    src="~/assets/images/mercado-livre-logo-8.png"
                    alt="Logo Mercado Livre"
                    class="h-6 w-auto"
                  />
                  <p class="text-base font-semibold text-yellow-700">
                    Produto Exclusivo do Site
                  </p>
                </div>
                <p class="text-sm text-gray-600">
                  Este é um produto exclusivo do nosso site. Se você prefere
                  comprar pelo Mercado Livre, entre em contato conosco que
                  podemos disponibilizar o anúncio.
                </p>
              </div>

              <!-- Modal de Login -->
              <LoginModal
                v-model="showLoginModal"
                title="Faça login para continuar"
                :icon="modalIcon"
                :description="modalMessage"
                @login-success="handleLoginSuccess"
              />

              <!-- Modal de Endereço -->
              <AddressModal v-model="showAddressModal" />

              <!-- Botão de Compra -->
              <div class="space-y-4">
                <!-- Removido indicador de carregamento do MercadoPago -->

                <div v-if="figure.value" class="w-full mt-4">
                  <button
                    @click="goToCheckout"
                    class="w-full py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-bold rounded-md flex items-center justify-center transition-colors duration-200"
                  >
                    <span class="mr-2">Comprar Agora</span>
                    <i class="fas fa-shopping-cart"></i>
                  </button>
                </div>

                <!-- Botão de Administrador para adicionar pedido -->
                <AdminOrderButton
                  v-if="authStore.isAdmin && figure.value"
                  :product="figure.value"
                />
              </div>
            </div>
          </div>
        </div>
        <div
          v-else-if="errorMessage"
          class="text-center py-12 max-w-2xl mx-auto"
        >
          <div
            class="bg-red-100 border border-red-400 text-red-700 px-6 py-5 rounded-lg shadow-md"
          >
            <div class="flex flex-col items-center">
              <i
                class="fas fa-exclamation-circle text-red-500 text-4xl mb-3"
              ></i>
              <h2 class="text-xl font-bold mb-2">Produto não encontrado</h2>
              <p class="mb-4">{{ errorMessage }}</p>
              <div class="mt-2">
                <NuxtLink
                  to="/"
                  class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-6 rounded-lg transition-colors duration-200"
                >
                  <i class="fas fa-home mr-2"></i>Ir para a página inicial
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-12">
          <div class="flex flex-col items-center">
            <i class="fas fa-spinner fa-spin text-4xl text-blue-500 mb-4"></i>
            <p class="text-xl text-gray-600">Carregando produto...</p>
          </div>
        </div>
      </div>
    </main>

    <!-- Modal de Login -->
    <LoginModal
      v-model="showLoginModal"
      title="Faça login para continuar"
      :icon="modalIcon"
      :description="modalMessage"
      @login-success="handleLoginSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onBeforeUnmount } from "vue";
import { useRoute } from "vue-router";
import { useAuthStore } from "~/stores/auth";
import AddressModal from "~/components/AddressModal.vue"; // Import do componente AddressModal
import FavoriteButton from "~/components/FavoriteButton.vue";
import AdminOrderButton from "~/components/admin/AdminOrderButton.vue"; // Import do componente AdminOrderButton
import { useHead } from "#imports";
import { useMLCheck } from "~/composables/useMLCheck";
import { Figure } from "~/types/figures";
import { parse, format, addDays } from "date-fns";
import { ptBR } from "date-fns/locale";

definePageMeta({
  validate: async (route) => {
    return Boolean(route.params.id);
  },
});

// const productsStore = useProductsStore();

// Definir o título da página com base na figura
definePageMeta({
  title: (route: any) => {
    return `Figura ${route.params.id}`;
  },
  validate: (route: any) => {
    return !!route.params.id;
  },
});

const route = useRoute();
const authStore = useAuthStore();
const { isAuthenticated } = authStore;
const { checkMLLink, isLinkWorking, checking, defaultMLLink } = useMLCheck();

// Variáveis de UI
const errorMessage = ref("");
const showLoginModal = ref(false);
const modalIcon = ref("fas fa-user text-red-500");
const modalMessage = ref("");
const currentImageIndex = ref(0);
const thumbnailVideos = ref<{ [key: string]: HTMLVideoElement }>({});

const description_1 = `<p class="mb-3">Sempre gostei de produzir coisas artesanais e comecei minha trajetória produzindo Funko Pops artesanais em biscuit. Com o tempo, evoluí para a impressão 3D em resina de alta qualidade — diferente das impressões convencionais, nossas peças têm acabamento suave, sem sinais ou marcas da impressão 3D convencional</p>

<p class="mb-4">Cada figura é produzida com cuidado artesanal, em pequena escala, o que garante atenção especial aos detalhes e um acabamento impecável.</p>

<p class="bg-yellow-50 p-3 rounded-lg mb-4 font-medium"><span class="text-yellow-600">💥 Produto exclusivo com Desconto Especial!</span><br>Este modelo ainda não foi produzido fisicamente — as imagens são renders digitais. Assim que a primeira peça for finalizada, o valor será ajustado para refletir o custo real de produção. Aproveite o preço promocional de pré-lançamento!</p>

<p class="text-green-600 font-medium">📩 Ficou com alguma dúvida? Fale com a gente, será um prazer te atender!</p>`;

const description_2 = `<p class="mb-3">Sempre gostei de produzir coisas artesanais e comecei minha trajetória produzindo Funko Pops artesanais em biscuit. Com o tempo, evoluí para a impressão 3D em resina de alta qualidade — diferente das impressões convencionais, nossas peças têm acabamento suave, sem sinais ou marcas da impressão 3D convencional</p>

<p class="mb-4">Cada figura é produzida com cuidado artesanal, em pequena escala, o que garante atenção especial aos detalhes e um acabamento impecável.</p>

<p class="bg-purple-50 p-3 rounded-lg mb-4 font-medium"><span class="text-purple-600">✅ Produto já produzido anteriormente!</span><br>Este modelo já foi produzido e testado, garantindo qualidade e fidelidade às imagens apresentadas. As fotos mostram o produto real que você receberá, com todos os detalhes e acabamentos.</p>

<p class="text-green-600 font-medium">📩 Ficou com alguma dúvida? Fale com a gente, será um prazer te atender!</p>`;

// Buscar dados do produto no servidor
const { data: figure } = await useFetch(`/api/figures/${route.params.id}`, {
  server: true,
  key: route.params.id as string,
  transform: (response: any) => {
    if (!response) return null;
    // Os dados já vêm serializados da API
    return response;
  },
  onResponseError: (error: any) => {
    console.error("Erro ao buscar produto:", error);
    errorMessage.value =
      "Produto não encontrado ou indisponível no momento. Você será redirecionado para a página inicial em alguns segundos...";

    // Redirecionar para a página inicial após um breve atraso
    if (typeof window !== "undefined") {
      setTimeout(() => {
        navigateTo("/");
      }, 3000); // Aumentamos para 3 segundos para dar tempo de ler a mensagem
    }

    return null;
  },
});

// Recriar instância de Figure no cliente
onMounted(() => {
  try {
    if (figure.value) {
      // Verificar se os dados essenciais estão presentes
      if (!figure.value.title || !figure.value.base_price) {
        console.error("Dados incompletos para o produto:", figure.value.id);
        errorMessage.value =
          "Produto não encontrado ou indisponível no momento.";
        return;
      }

      figure.value = new Figure(
        figure.value,
        figure.value.id,
        figure.value.coupons || []
      );

      // Log para debug do desconto
      console.log("Produto carregado com desconto:", {
        base_price: figure.value.base_price,
        finalPrice: figure.value.finalPrice,
        totalDiscount: figure.value.totalDiscount,
        coupons: figure.value.coupons,
      });
    }
  } catch (error) {
    console.error("Erro ao processar dados do produto:", error);
    errorMessage.value =
      "Erro ao carregar dados do produto. Por favor, tente novamente mais tarde.";
  }
});

function getRandomIncrement() {
  const min = 4.5;
  const max = 5;
  const step = 0.1;

  // Gera um índice aleatório entre 0 e o número de passos possíveis
  const randomIndex = Math.floor(Math.random() * ((max - min) / step + 1));

  // Retorna o valor correspondente ao índice gerado
  return (min + randomIndex * step).toFixed(1);
}

// Função para obter dimensões estruturadas para SEO
const getStructuredDimensions = () => {
  // Verificar se o produto existe e tem características
  if (!figure.value || !figure.value.characteristics) return null;

  const dimensions = [];

  // Verificar se a altura está definida
  if (figure.value.characteristics.height) {
    try {
      dimensions.push({
        "@type": "QuantitativeValue",
        name: "height",
        value: figure.value.characteristics.height.replace(/[^0-9.]/g, ""),
        unitCode: "CMT",
      });
    } catch (error) {
      console.error("Erro ao processar altura:", error);
    }
  }

  // Verificar se o peso está definido
  if (figure.value.characteristics.weight) {
    try {
      dimensions.push({
        "@type": "QuantitativeValue",
        name: "weight",
        value: figure.value.characteristics.weight.replace(/[^0-9.]/g, ""),
        unitCode: "GRM",
      });
    } catch (error) {
      console.error("Erro ao processar peso:", error);
    }
  }

  return dimensions.length > 0 ? dimensions : null;
};

// Dados estruturados para SEO
useHead(() => {
  // Se o produto não foi encontrado ou está oculto, retornar apenas o título básico
  if (!figure.value || (figure.value && figure.value.hidden)) {
    return {
      title: "Produto não encontrado | Fidel Figures",
      meta: [
        {
          name: "description",
          content:
            "Action Figures colecionáveis impressas em resina 3D. Modelos exclusivos e pintura manual!",
        },
        { name: "robots", content: "noindex, follow" },
      ],
    };
  }

  // Dados estruturados para SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: figure.value.title,
    description:
      figure.value.description ||
      (figure.value.previously_produced
        ? "Action Figure colecionável em resina 3D, pintado manualmente. Produto já produzido anteriormente, com qualidade garantida."
        : "Action Figure colecionável em resina 3D, pintado manualmente."),
    image:
      figure.value.gallery_imgs?.map((img: string) => ({
        "@type": "ImageObject",
        url: img,
        width: "800",
        height: "1000",
      })) || "",
    sku: figure.value.id,
    mpn: `FF-${figure.value.id}`,
    brand: {
      "@type": "Brand",
      name: "Fidel Figures",
    },
    keywords:
      "Action Figures, Colecionáveis, Resina 3D, Fidel Figures, Pintura Manual",
    offers: {
      "@type": "Offer",
      url: `https://fidelfigures.com.br/produto/${figure.value.id}`,
      priceCurrency: "BRL",
      price: figure.value.finalPrice.toFixed(2),
      priceValidUntil: new Date(
        new Date().setFullYear(new Date().getFullYear() + 1)
      ).toISOString(),
      itemCondition: "https://schema.org/NewCondition",
      availability: figure.value.previously_produced
        ? "https://schema.org/InStock"
        : "https://schema.org/PreOrder",
      ...(figure.value.previously_produced
        ? {}
        : {
            availabilityStarts: new Date(
              new Date().setDate(new Date().getDate() + 30)
            ).toISOString(),
          }),
      seller: {
        "@type": "Organization",
        name: "Fidel Figures",
        logo: "https://fidelfigures.com.br/logo.png",
        email: "<EMAIL>",
        telephone: "+5511999999999",
        sameAs: [
          "https://www.instagram.com/fidelfigures/",
          "https://www.facebook.com/fidelfigures/",
        ],
      },
      shippingDetails: {
        "@type": "OfferShippingDetails",
        shippingRate: {
          "@type": "MonetaryAmount",
          value: "0",
          currency: "BRL",
        },
        deliveryTime: {
          "@type": "ShippingDeliveryTime",
          businessDays: {
            "@type": "OpeningHoursSpecification",
            minValue: figure.value.previously_produced ? 2 : 5,
            maxValue: figure.value.previously_produced ? 5 : 10,
          },
        },
        shippingDestination: {
          "@type": "DefinedRegion",
          addressCountry: "BR",
        },
        transitTime: {
          "@type": "QuantitativeValue",
          minValue: figure.value.previously_produced ? 2 : 5,
          maxValue: figure.value.previously_produced ? 5 : 10,
          unitText: "dias",
        },
        handlingTime: {
          "@type": "QuantitativeValue",
          minValue: figure.value.previously_produced ? 1 : 1,
          maxValue: figure.value.previously_produced ? 2 : 3,
          unitText: "dias",
        },
      },
      hasMerchantReturnPolicy: {
        "@type": "MerchantReturnPolicy",
        applicableCountry: "BR",
        returnPolicyCategory:
          "https://schema.org/MerchantReturnFiniteReturnWindow",
        merchantReturnDays: 30,
        returnMethod: "https://schema.org/ReturnByMail",
        returnFees: "https://schema.org/FreeReturn",
      },
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: getRandomIncrement(),
      reviewCount: Math.floor(Math.random() * (250 - 150 + 1)) + 150,
    },
    productType: "Estátua",
    googleProductCategory: "3710",
    // Adicionar dimensões estruturadas se disponíveis
    ...(getStructuredDimensions()
      ? {
          weight: getStructuredDimensions()?.find(
            (d: any) => d.name === "weight"
          ),
          height: getStructuredDimensions()?.find(
            (d: any) => d.name === "height"
          ),
        }
      : {}),
    // Adicionar material
    material: "Resina 3D",
    // Adicionar categoria
    category: "Action Figures",
    // Adicionar informações de produção
    manufacturer: {
      "@type": "Organization",
      name: "Fidel Figures",
    },
  };

  const videoSchema =
    figure.value?.gallery_videos && figure.value.gallery_videos.length > 0
      ? {
          "@context": "http://schema.org",
          "@type": "VideoObject",
          name: figure.value?.title || "Produto",
          description: figure.value?.description || "",
          thumbnailUrl: figure.value?.gallery_imgs?.[0] || "",
          contentUrl: figure.value?.gallery_videos?.[0] || "",
          uploadDate: new Date().toISOString().split("T")[0],
          duration: "PT2M30S", // Duração do vídeo em ISO 8601
        }
      : null;

  const scripts = [
    {
      type: "application/ld+json",
      children: JSON.stringify(structuredData),
    },
  ];

  if (videoSchema) {
    scripts.push({
      type: "application/ld+json",
      children: JSON.stringify(videoSchema),
    });
  }

  return {
    script: scripts,
    title: figure.value?.alias
      ? `${figure.value.alias} | Fidel Figures - Action Figures em Resina`
      : "Produto não encontrado",
    meta: [
      {
        name: "description",
        content: figure.value?.description
          ? `${figure.value.description} ${
              figure.value.previously_produced
                ? "Produto já produzido anteriormente, com qualidade garantida."
                : ""
            } Compre agora sua action figure em resina 3D na Fidel Figures. Envio rápido para todo o Brasil!`
          : figure.value?.previously_produced
          ? "Action Figures colecionáveis impressas em resina 3D. Produto já produzido anteriormente, com qualidade garantida. Modelos exclusivos e pintura manual!"
          : "Action Figures colecionáveis impressas em resina 3D. Modelos exclusivos e pintura manual!",
      },
      { property: "og:title", content: figure.value?.alias || "" },
      {
        property: "og:description",
        content: figure.value?.description
          ? `${figure.value.description} ${
              figure.value.previously_produced
                ? "Produto já produzido anteriormente, com qualidade garantida."
                : ""
            }`
          : figure.value?.previously_produced
          ? "Action Figures colecionáveis impressas em resina 3D. Produto já produzido anteriormente, com qualidade garantida."
          : "Action Figures colecionáveis impressas em resina 3D. Modelos exclusivos e pintura manual!",
      },
      { property: "og:image", content: figure.value?.gallery_imgs?.[0] || "" },
      {
        property: "og:url",
        content: `https://fidelfigures.com.br/produto/${route.params.id}`,
      },
      { name: "robots", content: "index, follow" },
      {
        name: "transitTime",
        content: figure.value.previously_produced ? "2-5 dias" : "5-10 dias",
      },
      {
        name: "handlingTime",
        content: figure.value.previously_produced ? "1-2 dias" : "1-3 dias",
      },

      // Melhorias: Twitter Cards
      { name: "twitter:card", content: "summary_large_image" },
      { name: "twitter:title", content: figure.value?.alias || "" },
      {
        name: "twitter:description",
        content: figure.value?.description
          ? `${figure.value.description} ${
              figure.value.previously_produced
                ? "Produto já produzido anteriormente, com qualidade garantida."
                : ""
            }`
          : figure.value?.previously_produced
          ? "Action Figures colecionáveis impressas em resina 3D. Produto já produzido anteriormente, com qualidade garantida."
          : "Action Figures colecionáveis impressas em resina 3D. Modelos exclusivos e pintura manual!",
      },
      { name: "twitter:image", content: figure.value?.gallery_imgs?.[0] || "" },
    ],
    link: [
      {
        rel: "canonical",
        href: `https://fidelfigures.com.br/produto/${route.params.id}`,
      },
    ],
  };
});

const config = useRuntimeConfig();
const productionTime = parseInt(config.public.PRODUCTION_TIME || "60");
const startDate = config.public.PRODUCTION_START_DATE || "20 de março";

const formatProductionMessage = computed(() => {
  return `Produto feito sob encomenda - Prazo de ${productionTime} dias corridos após o início da produção.`;
});

const formatDeliveryDate = computed(() => {
  // Para produtos sob encomenda, usar a lógica original
  const parsedStartDate = parse(startDate, "d 'de' MMMM", new Date(), {
    locale: ptBR,
  });
  const deliveryDate = addDays(parsedStartDate, productionTime);
  const formattedDeliveryDate = format(deliveryDate, "d 'de' MMMM", {
    locale: ptBR,
  });

  return `Início da produção em ${startDate} com previsão de entrega para ${formattedDeliveryDate}`;
});

// Variáveis relacionadas ao MercadoPago removidas
// const preferenceId = ref<string>();
// const showPaymentOptions = ref(false);
// const loadingPreference = ref(false);
// Removida variável selectedPaymentMethod

// Função para capturar o primeiro frame do vídeo como poster
const handleVideoLoad = (video: HTMLVideoElement, mediaUrl: string) => {
  thumbnailVideos.value[mediaUrl] = video;
  video.currentTime = 0.1; // Pega um frame logo no início
};

// Prefetch das imagens e vídeos da galeria
onMounted(() => {
  // Prefetch das imagens
  if (figure.value?.gallery_imgs) {
    figure.value.gallery_imgs.forEach((img: string) => {
      const link = document.createElement("link");
      link.rel = "prefetch";
      link.href = img;
      document.head.appendChild(link);
    });
  }

  // Prefetch dos vídeos
  if (figure.value?.gallery_videos && figure.value.gallery_videos.length > 0) {
    console.log("🎬 Prefetch de vídeos:", figure.value.gallery_videos);
    figure.value.gallery_videos.forEach((video: string) => {
      // Para vídeos, usamos preload em vez de prefetch para melhor compatibilidade
      const link = document.createElement("link");
      link.rel = "preload";
      link.href = video;
      link.as = "video";
      document.head.appendChild(link);
    });
  }
});

onBeforeUnmount(() => {
  thumbnailVideos.value = {};
});

// Verificar link do ML quando o figure mudar
watch(
  () => figure.value,
  async (newFigure) => {
    if (newFigure?.id) {
      const mlUrl = `https://produto.mercadolivre.com.br/${getProductId(
        newFigure.id
      )}`;
      await checkMLLink(mlUrl);
    }
  },
  { immediate: true }
);

// Removido watch para createPreference

const allMedia = computed(() => {
  if (!figure.value) return [];
  return [
    ...(figure.value.gallery_imgs || []),
    ...(figure.value.gallery_videos || []),
  ];
});

const currentImage = computed(() => {
  return allMedia.value[currentImageIndex.value] || "";
});

const discount_percentage = computed(() => {
  // Se o produto não foi encontrado, retornar 0
  if (!figure.value) return 0;

  console.log("Calculando discount_percentage:", {
    "figure.value?.totalDiscount": figure.value?.totalDiscount,
    "figure.value?.base_price": figure.value?.base_price,
    "figure.value?.finalPrice": figure.value?.finalPrice,
    "figure.value?.coupons": figure.value?.coupons,
  });
  return figure.value?.totalDiscount || 0; // Garantir que retorne 0 se for undefined
});

// Referência ao cliente removida pois não é mais necessária nesta página

// Removido watch para createPreference quando o endereço muda
// watch(
//   () => hasAddress.value,
//   async (newHasAddress) => {
//     if (newHasAddress) {
//       await createPreference();
//     }
//   }
// );

// Função createPreference removida - agora apenas na página de checkout

// Função initWallet e watch do preferenceId removidos - agora apenas na página de checkout

function getProductId(idML: string) {
  if (!idML) return idML;
  const id = idML.split("MLB");
  return `MLB-${id[1]}`;
}

const handleLoginSuccess = () => {
  showLoginModal.value = false;
  // Removida chamada para createPreference
};

// Funções relacionadas ao Stripe foram removidas pois agora são tratadas na página de checkout

function isWebView() {
  // Usando navigator.userAgent diretamente sem vendor
  const userAgent = navigator.userAgent;
  return /FBAN|FBAV|Instagram|Twitter|WebView|wv/i.test(userAgent);
}

const handleRequireLogin = (action: string) => {
  if (isWebView()) {
    // Tenta abrir no navegador externo (iOS e Android)
    const externalUrl = "https://fidelfigures.com.br/login"; // URL que contém o login do Firebase
    window.open(externalUrl, "_blank"); // Abre o link fora do WebView
    return; // Impede a execução do login dentro do WebView
  }

  // Salvar a URL atual para redirecionamento após o login
  if (typeof window !== "undefined") {
    localStorage.setItem("auth_return_url", window.location.pathname);
  }

  showLoginModal.value = true;
  if (action === "payment") {
    modalIcon.value = "fas fa-shopping-cart text-red-500";
    modalMessage.value = "Faça login para continuar com a compra";
  } else if (action === "favorite") {
    modalIcon.value = "fas fa-heart text-red-500";
    modalMessage.value = "Faça login para adicionar aos favoritos";
  }
};

// Removida limpeza do container do wallet, pois não estamos mais usando MercadoPago nesta página

const isVideo = (url: string) => {
  if (!url) return false;

  // Decodifica a URL do Firebase para verificar o caminho real
  try {
    const decodedUrl = decodeURIComponent(url);

    // Verifica se é um vídeo baseado no caminho ou extensão
    return (
      // Verifica o caminho do Firebase Storage
      (decodedUrl.includes("/products/") && decodedUrl.includes("/videos/")) ||
      // Verifica extensões comuns de vídeo
      decodedUrl.endsWith(".mp4") ||
      decodedUrl.endsWith(".webm") ||
      decodedUrl.endsWith(".mov") ||
      decodedUrl.endsWith(".avi") ||
      decodedUrl.endsWith(".mkv") ||
      // Verifica se a URL contém parâmetros que indicam vídeo
      decodedUrl.includes("video")
    );
  } catch {
    // Se não conseguir decodificar a URL, tenta verificar a string original
    return (
      url.includes("/videos/") ||
      url.endsWith(".mp4") ||
      url.endsWith(".webm") ||
      url.endsWith(".mov") ||
      url.endsWith(".avi") ||
      url.endsWith(".mkv")
    );
  }
};

// Formata medidas adicionando unidades quando necessário
const formatMeasure = (
  value: string | undefined,
  type: "weight" | "height"
) => {
  if (!value) return "";

  // Se já contém unidade, retorna como está
  if (value.toLowerCase().includes("g") || value.toLowerCase().includes("cm")) {
    return value;
  }

  // Adiciona unidade apropriada
  return type === "weight" ? `${value}g` : `${value}cm`;
};

// Função getStructuredDimensions foi movida para antes do useHead

// Estado para mostrar o modal de endereço
const showAddressModal = ref(false);

// Função para navegar para a página de checkout
const goToCheckout = () => {
  // Verificar se o produto existe
  if (!figure.value) {
    console.error("❌ [Produto] Produto não encontrado");
    errorMessage.value = "Produto não encontrado ou indisponível no momento.";
    return;
  }

  // Verificar se o usuário está autenticado
  if (!isAuthenticated) {
    console.log(
      "🔐 [Produto] Usuário não autenticado, redirecionando para login"
    );
    // Salvar a URL de checkout como URL de retorno
    if (typeof window !== "undefined") {
      const checkoutUrl = `/checkout?productId=${figure.value.id}`;
      console.log(`💾 [Produto] Salvando URL de retorno: ${checkoutUrl}`);
      localStorage.setItem("auth_return_url", checkoutUrl);

      // Verificar se foi salvo corretamente
      const savedUrl = localStorage.getItem("auth_return_url");
      console.log(`💾 [Produto] URL de retorno salva: ${savedUrl}`);
    }
    // Redirecionar para a página de login
    console.log("🔙 [Produto] Redirecionando para login");
    navigateTo("/login");
    return;
  }

  // Se o usuário está autenticado, navegar diretamente para o checkout
  console.log("🔐 [Produto] Usuário autenticado, navegando para checkout");
  navigateTo(`/checkout?productId=${figure.value.id}`);
};
</script>
