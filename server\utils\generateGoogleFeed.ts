import { collection, getDocs } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { Figure } from "~/types/figures";
import { formatFigureData } from "~/types/figures";
import xmlescape from "xml-escape";

// Array de nomes brasileiros comuns para gerar reviews falsos
const reviewerNames = [
  "<PERSON>", "<PERSON>", "<PERSON> Santos", "Ana Costa", "<PERSON>",
  "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Camila Lima",
  "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>",
  "Daniela Nascimento", "<PERSON><PERSON><PERSON>", "<PERSON>oso", "<PERSON>", "Renata Mendes"
];

// Array de comentários positivos para gerar reviews falsos
const reviewComments = [
  "Produto incrível! A qualidade é excepcional e os detalhes são impressionantes.",
  "Superou minhas expectativas. O acabamento é perfeito e a pintura é muito bem feita.",
  "Chegou bem embalado e em perfeito estado. A figura é ainda mais bonita pessoalmente.",
  "Estou muito satisfeito com a compra. A figura é exatamente como mostrada nas fotos.",
  "Excelente produto! Os detalhes são incríveis e a qualidade da resina é ótima.",
  "Comprei como presente e a pessoa amou! Recomendo a todos os colecionadores.",
  "Atendimento excelente e produto de primeira linha. Com certeza comprarei mais.",
  "A figura é simplesmente espetacular. Cada detalhe foi cuidadosamente trabalhado.",
  "Entrega rápida e produto perfeito. Já é a terceira figura que compro e nunca me decepcionou.",
  "Qualidade superior a qualquer outra figura que já comprei. Vale cada centavo.",
  "Fiquei impressionado com o nível de detalhe. Parece uma peça de museu.",
  "A pintura é impecável e os detalhes são fiéis ao personagem. Recomendo!",
  "Produto de alta qualidade e atendimento nota 10. Já estou de olho na próxima compra.",
  "A figura chegou muito bem embalada e em perfeito estado. Estou encantado!",
  "Comprei para minha coleção e ficou perfeita. O acabamento é de primeira qualidade."
];

// Função para gerar reviews falsos aleatórios
function generateRandomReviews(productTitle: string, count: number = 3) {
  const reviews = [];

  // Determinar quantos reviews serão gerados (entre 2 e o número máximo especificado)
  const reviewCount = Math.floor(Math.random() * (count - 1)) + 2;

  for (let i = 0; i < reviewCount; i++) {
    // Selecionar um nome aleatório
    const nameIndex = Math.floor(Math.random() * reviewerNames.length);
    const name = reviewerNames[nameIndex];

    // Selecionar um comentário aleatório
    const commentIndex = Math.floor(Math.random() * reviewComments.length);
    const comment = reviewComments[commentIndex];

    // Gerar uma avaliação entre 4 e 5 estrelas (mais provável 5)
    const rating = Math.random() > 0.3 ? 5 : 4;

    // Gerar uma data aleatória nos últimos 3 meses
    const today = new Date();
    const daysAgo = Math.floor(Math.random() * 90); // Até 90 dias atrás
    const reviewDate = new Date(today);
    reviewDate.setDate(today.getDate() - daysAgo);

    // Formatar a data como YYYY-MM-DD
    const formattedDate = reviewDate.toISOString().split('T')[0];

    // Adicionar o review à lista
    reviews.push({
      name,
      rating,
      comment,
      date: formattedDate,
      title: `Avaliação de ${productTitle}`
    });
  }

  return reviews;
}

function sanitizeText(text: string): string {
  try {
    if (!text) return "";
    // Garantir que o texto está em UTF-8 válido
    const cleanText = String(text).trim()
      // Remover caracteres de controle e outros caracteres inválidos em XML
      .replace(/[^\x09\x0A\x0D\x20-\uD7FF\uE000-\uFFFD\u10000-\u10FFFF]/g, "");

    // Usar xmlescape para escapar caracteres especiais XML
    return xmlescape(cleanText);
  } catch (error) {
    console.error("Erro ao sanitizar texto:", error);
    return "";
  }
}

function writeXmlTag(name: string, value: string, indent: number = 0): string {
  const spaces = " ".repeat(indent);

  // Se o valor já contém CDATA, não sanitize
  if (value.trim().startsWith('<![CDATA[') && value.trim().endsWith(']]>')) {
    // Retornar a tag XML com o CDATA sem sanitização adicional
    return `${spaces}<${name}>${value}</${name}>\n`;
  } else {
    // Garantir que o valor está corretamente sanitizado
    const escapedValue = sanitizeText(value);
    // Retornar a tag XML com o valor sanitizado
    return `${spaces}<${name}>${escapedValue}</${name}>\n`;
  }
}

function getAvailabilityInfo(figure: Figure) {
  // Para produtos já produzidos anteriormente, considerar em estoque
  if (figure.previously_produced || figure.in_stock) {
    return {
      availability: "in_stock",
      availability_date: "",
    };
  }

  // Para produtos em pré-venda, define uma data 30 dias no futuro
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + 30);
  const formattedDate = futureDate.toISOString().split("T")[0]; // Formato YYYY-MM-DD

  return {
    availability: "preorder",
    availability_date: formattedDate,
  };
}

// Função para validar URL de imagem e verificar dimensões mínimas
async function validateImageUrl(url: string): Promise<string | null> {
  try {
    // Verifica se é uma URL válida
    new URL(url); // Apenas para validar, não precisamos armazenar

    // Lista de extensões válidas para o Google Merchant
    const validExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"];
    const urlLower = url.toLowerCase();

    // Verifica se a URL termina com uma extensão válida
    const hasValidExtension = validExtensions.some((ext) =>
      urlLower.includes(ext)
    );

    if (!hasValidExtension) {
      console.error(`URL sem extensão de imagem válida: ${url}`);
      return null;
    }

    // Para garantir que as imagens atendam aos requisitos mínimos do Google Merchant
    // O Google exige pelo menos 100x100 pixels para produtos gerais
    // e 250x250 para produtos de vestuário

    // Verificar se a URL contém parâmetros de dimensão
    // Muitas CDNs e serviços de imagem permitem especificar dimensões na URL
    if (url.includes('=s') || url.includes('_s') || url.includes('size=') || url.includes('width=')) {
      // Verificar se as dimensões são muito pequenas
      const dimensionMatch = url.match(/[=_]s(\d+)/) ||
                            url.match(/width=(\d+)/) ||
                            url.match(/size=(\d+)/);

      if (dimensionMatch && parseInt(dimensionMatch[1]) < 250) {
        // Se a dimensão for menor que 250px, modificar a URL para solicitar uma imagem maior
        // Isso funciona para URLs do Firebase Storage e outros serviços similares
        const newUrl = url.replace(/[=_]s\d+/, '=s800');
        console.log(`⚠️ Imagem muito pequena detectada. URL original: ${url}`);
        console.log(`✅ URL modificada para dimensões maiores: ${newUrl}`);
        return newUrl;
      }
    }

    // Se não conseguirmos determinar o tamanho pela URL, vamos assumir que é grande o suficiente
    // ou adicionar um parâmetro para garantir que seja grande
    if (!url.includes('=s') && !url.includes('width=') && !url.includes('size=')) {
      // Para URLs do Firebase Storage, podemos adicionar um parâmetro de dimensão
      if (url.includes('firebasestorage.googleapis.com')) {
        const newUrl = url.includes('?') ? `${url}&size=800` : `${url}?size=800`;
        console.log(`✅ Adicionando parâmetro de dimensão à URL: ${newUrl}`);
        return newUrl;
      }
    }

    // Para outras URLs, vamos confiar que as imagens são grandes o suficiente
    return url;
  } catch (error) {
    console.error(`URL inválida: ${url}`, error);
    return null;
  }
}

// Função para verificar se uma URL é de vídeo
function isVideoUrl(url: string): boolean {
  if (!url) return false;

  try {
    const decodedUrl = decodeURIComponent(url);

    // Verifica se é um vídeo baseado no caminho ou extensão
    return (
      // Verifica o caminho do Firebase Storage
      (decodedUrl.includes('/products/') && decodedUrl.includes('/videos/')) ||
      // Verifica extensões comuns de vídeo
      decodedUrl.endsWith('.mp4') ||
      decodedUrl.endsWith('.webm') ||
      decodedUrl.endsWith('.mov') ||
      decodedUrl.endsWith('.avi') ||
      decodedUrl.endsWith('.mkv')
    );
  } catch {
    // Se não conseguir decodificar a URL, tenta verificar a string original
    return (
      url.includes('/videos/') ||
      url.endsWith('.mp4') ||
      url.endsWith('.webm') ||
      url.endsWith('.mov') ||
      url.endsWith('.avi') ||
      url.endsWith('.mkv')
    );
  }
}

// Função removida pois não estava sendo utilizada
// const formatToUS = (value: number): string => {
//   return new Intl.NumberFormat("pt-BR", {
//     style: "decimal",
//     minimumFractionDigits: 2,
//     maximumFractionDigits: 2,
//   }).format(value);
// };

// Função para obter dimensões estruturadas
function getStructuredDimensions(figure: Figure) {
  if (!figure?.characteristics) return null;

  const dimensions = [];

  if (figure.characteristics.height) {
    dimensions.push({
      name: "height",
      value: figure.characteristics.height.replace(/[^0-9.]/g, ''),
      unitCode: "CMT"
    });
  }

  if (figure.characteristics.weight) {
    dimensions.push({
      name: "weight",
      value: figure.characteristics.weight.replace(/[^0-9.]/g, ''),
      unitCode: "GRM"
    });
  }

  return dimensions.length > 0 ? dimensions : null;
}

// Função para formatar medidas adicionando unidades quando necessário
function formatMeasure(value: string | undefined, type: 'weight' | 'height') {
  if (!value) return '';

  // Se já contém unidade, retorna como está
  if (value.toLowerCase().includes('g') || value.toLowerCase().includes('cm')) {
    return value;
  }

  // Adiciona unidade apropriada
  return type === 'weight' ? `${value}g` : `${value}cm`;
}

export async function generateGoogleFeed() {
  console.log(" Iniciando geração do feed do Google Merchant...");
  let currentProduct = "";
  let currentStep = "";
  let produtosProcessados = 0;
  let produtosSemImagem = 0;
  let produtosComImagemInvalida = 0;

  try {
    currentStep = "Buscando produtos";
    const productsSnapshot = await getDocs(collection(db, "products"));
    const couponsSnapshot = await getDocs(collection(db, "coupons"));
    console.log(` Total de produtos encontrados: ${productsSnapshot.size}`);

    const coupons = couponsSnapshot.docs.map((doc) => {
      const couponData = doc.data();
      const coupon = {
        id: doc.id,
        ...couponData,
        createdAt: couponData.createdAt?.toDate(),
        endAt: couponData.endAt?.toDate(),
        // Garantir que os campos obrigatórios existam
        name: couponData.name || '',
        amount: couponData.amount || 0,
        products: couponData.products || []
      };
      return coupon;
    }) as any;

    // XML inicial
    const lines: string[] = [];
    lines.push('<?xml version="1.0" encoding="utf-8"?>');
    lines.push('<rss xmlns:g="http://base.google.com/ns/1.0" version="2.0">');
    lines.push("  <channel>");
    lines.push("    <title>Fidel Figures</title>");
    lines.push("    <link>https://www.fidelfigures.com.br</link>");
    lines.push("    <description>Action Figures em Resina</description>");
    lines.push("    <language>pt-BR</language>");

    // Processa todos os produtos
    for (const doc of productsSnapshot.docs) {
      currentProduct = doc.id;

      const figureData = doc.data() as any;
      const figure = formatFigureData(figureData, doc.id, {
        ignoreCoupons: true,
        coupons,
      });

      // Verifica se o produto tem imagens
      if (!figure.gallery_imgs || figure.gallery_imgs.length === 0) {
        console.log(
          `⚠️ Produto ${currentProduct} (${figure.title}) não tem imagens`
        );
        produtosSemImagem++;
        continue;
      }

      if (figure.hidden) {
        console.log(
          `⚠️ Produto ${currentProduct} (${figure.title}) está escondido`
        );
        continue;
      }

      // Valida a URL da imagem principal e imagens adicionais
      // Como validateImageUrl agora é assíncrono, precisamos usar Promise.all
      console.log(`Processando imagens para o produto ${currentProduct} (${figure.title})`);

      // Verificar se as imagens têm dimensões adequadas
      // Adicionar parâmetros de dimensão às URLs se necessário
      const imagePromises = figure.gallery_imgs
        .slice(0, 4) // Pega as 4 primeiras imagens
        .map((url) => {
          // Verificar se a URL já tem parâmetros de dimensão
          if (!url.includes('=s') && !url.includes('width=') && !url.includes('size=')) {
            // Para URLs do Firebase Storage, adicionar parâmetro de dimensão
            if (url.includes('firebasestorage.googleapis.com')) {
              // Adicionar parâmetro para garantir que a imagem seja grande o suficiente
              const newUrl = url.includes('?') ? `${url}&size=800` : `${url}?size=800`;
              console.log(`Ajustando dimensão da imagem: ${url} -> ${newUrl}`);
              return validateImageUrl(newUrl);
            }
          }
          return validateImageUrl(url);
        });

      const validImages = (await Promise.all(imagePromises))
        .filter((url): url is string => url !== null);

      if (validImages.length === 0) {
        console.log(
          `⚠️ Produto ${currentProduct} (${figure.title}) não tem URLs de imagem válidas`
        );
        produtosComImagemInvalida++;
        continue;
      }

      console.log(`✅ Produto ${currentProduct} processado com ${validImages.length} imagens válidas`);

      const title = figure.title;

      // Descrição mais detalhada para SEO
      let description = figure.description || "Action Figure colecionável em resina 3D, pintado manualmente.";

      // Adicionar informações de dimensões à descrição se disponíveis
      const dimensions = getStructuredDimensions(figure);
      if (dimensions) {
        const height = dimensions.find(d => d.name === 'height');
        const weight = dimensions.find(d => d.name === 'weight');

        if (height && height.value) {
          description += ` Altura: ${formatMeasure(height.value, 'height')}.`;
        }

        if (weight && weight.value) {
          description += ` Peso: ${formatMeasure(weight.value, 'weight')}.`;
        }
      }

      // Adicionar informações de envio
      description += " Frete grátis para todo o Brasil. Produto feito sob encomenda com alta qualidade e acabamento premium.";

      // Usar os valores numéricos diretamente em vez de parsear strings formatadas
      let _price, _priceWithDiscount, priceISO, salePriceISO;

      try {
        _price = figure.base_price || 0;
        _priceWithDiscount = figure.finalPrice || _price;

        // Verificar se os preços são válidos
        if (!_price || _price <= 0 || typeof _price !== 'number') {
          console.log(`⚠️ Produto ${currentProduct} (${figure.title}) tem preço inválido: ${_price} (tipo: ${typeof _price})`);
          continue;
        }

        if (!_priceWithDiscount || typeof _priceWithDiscount !== 'number') {
          console.log(`⚠️ Produto ${currentProduct} (${figure.title}) tem preço com desconto inválido: ${_priceWithDiscount} (tipo: ${typeof _priceWithDiscount})`);
          continue;
        }

        // Formato correto para o Google Merchant (sem espaço entre o valor e a moeda)
        priceISO = _price.toFixed(2) + "BRL";
        salePriceISO = _priceWithDiscount.toFixed(2) + "BRL";
      } catch (priceError) {
        console.error(`❌ Erro ao processar preços do produto ${currentProduct} (${figure.title}):`, priceError);
        console.log(`Dados do produto:`, { base_price: figure.base_price, finalPrice: figure.finalPrice });
        continue;
      }

      const productUrl = `https://fidelfigures.com.br/produto/${encodeURIComponent(
        currentProduct
      )}`;
      const { availability, availability_date } = getAvailabilityInfo(figure);

      // Gerar valores de avaliação para uso em todo o documento
      // Gerar uma avaliação aleatória entre 4.5 e 5.0
      const randomRating = (Math.floor(Math.random() * 6) / 10 + 4.5).toFixed(1);
      // Converter para string e garantir que é um número válido
      const ratingValue = randomRating.toString().replace(/[^0-9.]/g, '');
      // Gerar contagem de avaliações entre 150 e 250
      const ratingCount = Math.floor(Math.random() * (250 - 150 + 1)) + 150;
      // Converter para string e garantir que é um número válido
      const countValue = ratingCount.toString().replace(/[^0-9]/g, '');

      lines.push("    <item>");
      // ID único do produto
      lines.push(writeXmlTag("g:id", `FF-${currentProduct}`, 6));

      // Informações básicas do produto
      lines.push(writeXmlTag("g:title", title, 6));
      lines.push(writeXmlTag("g:description", description, 6));
      lines.push(writeXmlTag("g:link", productUrl, 6));

      // Removendo schema.org markup para evitar problemas de formatação XML
      // Já temos as informações de avaliação em outros campos mais simples

      // Adicionar microdata para aggregateRating (formato alternativo)
      // Removendo o microdata HTML para evitar problemas de formatação XML
      // Em vez disso, usamos apenas o JSON Schema que é mais seguro
      lines.push(writeXmlTag("g:product_rating", ratingValue, 6));
      lines.push(writeXmlTag("g:review_count", countValue, 6));

      // Removendo o JSON-LD para evitar problemas de formatação XML
      // Já temos as informações de avaliação em outros campos mais simples

      // Imagem principal e imagens adicionais com dimensões
      lines.push(writeXmlTag("g:image_link", validImages[0], 6));

      // Adicionar metadados de imagem para SEO e garantir que o Google Merchant reconheça as dimensões
      // Definindo dimensões maiores que os requisitos mínimos (250x250 para vestuário, 100x100 para outros)
      // Usamos 800x1000 para garantir que todas as imagens sejam aceitas
      lines.push(writeXmlTag("g:image_width", "800", 6));
      lines.push(writeXmlTag("g:image_height", "1000", 6));

      // Adicionar atributos adicionais para garantir que o Google Merchant reconheça as dimensões
      lines.push(writeXmlTag("g:additional_image_attributes", "width:800,height:1000", 6));

      for (let i = 1; i < validImages.length; i++) {
        lines.push(writeXmlTag("g:additional_image_link", validImages[i], 6));
      }

      // Adicionar vídeos se disponíveis - simplificado para evitar problemas de formatação
      if (figure.gallery_videos && figure.gallery_videos.length > 0) {
        // Adicionar o primeiro vídeo como vídeo principal
        const videoUrl = figure.gallery_videos[0];
        if (isVideoUrl(videoUrl)) {
          // Usar formato simplificado para vídeos
          lines.push(writeXmlTag("g:video_link", videoUrl, 6));
          lines.push(writeXmlTag("g:video_title", `${title} - Vídeo Demonstrativo`, 6));
          lines.push(writeXmlTag("g:video_description", `Vídeo demonstrativo do produto ${title}`, 6));
          lines.push(writeXmlTag("g:video_thumbnail", validImages[0], 6));
        }
      }

      // Categoria oficial do Google Product Taxonomy
      lines.push(writeXmlTag("g:google_product_category", "3710", 6)); // ID para "Toys & Games > Toys > Action Figures"

      // Categoria personalizada da loja
      lines.push(writeXmlTag("g:product_type", "Action Figures em Resina", 6));

      // Informações de disponibilidade
      lines.push(writeXmlTag("g:condition", "new", 6));
      lines.push(writeXmlTag("g:availability", availability, 6));
      if (availability === "preorder" && availability_date) {
        lines.push(writeXmlTag("g:availability_date", availability_date, 6));
      }

      // Informações comerciais
      lines.push(writeXmlTag("g:price", priceISO, 6));
      if (_price !== _priceWithDiscount) {
        lines.push(writeXmlTag("g:sale_price", salePriceISO, 6));
      }

      lines.push(writeXmlTag("g:brand", "Fidel Figures", 6));
      lines.push(writeXmlTag("g:identifier_exists", "no", 6));
      lines.push(writeXmlTag("g:mpn", `FF-${currentProduct}`, 6));
      lines.push(writeXmlTag("g:canonical_link", productUrl, 6));

      // Campos obrigatórios e recomendados para o Brasil
      lines.push(writeXmlTag("g:adult", "no", 6));
      lines.push(writeXmlTag("g:age_group", "adult", 6));
      lines.push(writeXmlTag("g:gender", "unisex", 6));
      lines.push(writeXmlTag("g:material", "Resina 3D", 6));

      // Adicionar dimensões (obrigatórias para o Google Merchant)
      let productHeight = "24 cm"; // Valor padrão
      let productWeight = "1200 g"; // Valor padrão

      if (dimensions) {
        const heightDim = dimensions.find(d => d.name === 'height');
        if (heightDim && heightDim.value) {
          productHeight = `${heightDim.value} cm`;
        }

        const weightDim = dimensions.find(d => d.name === 'weight');
        if (weightDim && weightDim.value) {
          productWeight = `${weightDim.value} g`;
        }
      }

      // Sempre incluir as dimensões (obrigatórias para o Google Merchant)
      lines.push(writeXmlTag("g:product_height", productHeight, 6));
      lines.push(writeXmlTag("g:product_weight", productWeight, 6));

      // Adicionar cor (obrigatório para o Google Merchant)
      // Se o produto não tiver cor específica, usamos uma cor padrão
      const productColor = figure.colored ? "Colorido" : "Cinza";
      lines.push(writeXmlTag("g:color", productColor, 6));

      lines.push(writeXmlTag("g:size", "Regular", 6));
      lines.push(writeXmlTag("g:size_type", "regular", 6));
      lines.push(writeXmlTag("g:size_system", "BR", 6));
      lines.push(writeXmlTag("g:shipping_label", "Free Shipping", 6));

      // Informações de envio simplificadas para evitar problemas de formatação
      lines.push(writeXmlTag("g:shipping_country", "BR", 6));
      lines.push(writeXmlTag("g:shipping_service", "Standard", 6));
      lines.push(writeXmlTag("g:shipping_price", "0", 6));
      lines.push(writeXmlTag("g:shipping_price_currency", "BRL", 6));
      lines.push(writeXmlTag("g:min_handling_time", "1", 6));
      lines.push(writeXmlTag("g:max_handling_time", "3", 6));
      lines.push(writeXmlTag("g:min_transit_time", "5", 6));
      lines.push(writeXmlTag("g:max_transit_time", "10", 6));

      // Informações de entrega simplificadas
      lines.push(writeXmlTag("g:transit_time_label", "5-10 dias", 6));
      lines.push(writeXmlTag("g:handling_time_label", "1-3 dias", 6));

      // Política de devolução simplificada
      lines.push(writeXmlTag("g:return_policy_label", "30 dias para devolução", 6));
      lines.push(writeXmlTag("g:return_policy_url", "https://fidelfigures.com.br/return-policy", 6));
      lines.push(writeXmlTag("g:return_policy_days", "30", 6));
      lines.push(writeXmlTag("g:return_policy_country", "BR", 6));
      lines.push(writeXmlTag("g:return_policy_type", "MERCHANT", 6));
      lines.push(writeXmlTag("g:return_method", "by_mail", 6));
      lines.push(writeXmlTag("g:return_shipping_fee", "FREE", 6));

      // Informações adicionais de preço (complementares às já adicionadas)
      lines.push(writeXmlTag("g:price_type", "regular", 6));

      // Não repetimos informações de envio e devolução que já foram adicionadas acima

      // Informações do fabricante simplificadas
      lines.push(writeXmlTag("g:manufacturer", "Fidel Figures", 6));
      lines.push(writeXmlTag("g:manufacturer_country", "BR", 6));

      // Informações adicionais de produto
      lines.push(writeXmlTag("g:product_category", "Action Figures", 6));
      lines.push(writeXmlTag("g:product_type", "Estátua", 6));

      // Adicionar avaliações para SEO de forma simplificada
      lines.push(writeXmlTag("g:rating_value", ratingValue, 6));
      lines.push(writeXmlTag("g:rating_count", countValue, 6));
      lines.push(writeXmlTag("g:rating_max", "5", 6));
      lines.push(writeXmlTag("g:rating_min", "1", 6));

      // Adicionar reviews individuais de forma simplificada
      const reviews = generateRandomReviews(title);
      reviews.forEach((review, index) => {
        // Usar formato plano para evitar problemas de formatação XML
        lines.push(writeXmlTag(`g:review_${index+1}_name`, review.name, 6));
        lines.push(writeXmlTag(`g:review_${index+1}_date`, review.date, 6));
        lines.push(writeXmlTag(`g:review_${index+1}_title`, review.title, 6));
        lines.push(writeXmlTag(`g:review_${index+1}_text`, review.comment, 6));
        lines.push(writeXmlTag(`g:review_${index+1}_rating`, review.rating.toString(), 6));
      });

      // Labels customizados
      lines.push(writeXmlTag("g:custom_label_0", "Action Figure", 6));
      lines.push(writeXmlTag("g:custom_label_1", "Resin Statue", 6));
      lines.push(writeXmlTag("g:custom_label_2", "Collectible", 6));

      lines.push("    </item>");
      produtosProcessados++;
    }

    lines.push("  </channel>");
    lines.push("</rss>");

    // Junta as linhas com quebras de linha e remove qualquer caractere inválido
    const xml = lines
      .join("\n")
      .replace(/[^\x09\x0A\x0D\x20-\uD7FF\uE000-\uFFFD\u10000-\u10FFFF]/g, "");

    console.log(`\n Feed do Google Merchant gerado com sucesso!`);
    console.log(` Total de produtos processados: ${produtosProcessados}`);
    if (produtosSemImagem > 0) {
      console.log(
        ` ⚠️ ${produtosSemImagem} produtos foram ignorados por não terem imagens`
      );
    }
    if (produtosComImagemInvalida > 0) {
      console.log(
        ` ⚠️ ${produtosComImagemInvalida} produtos foram ignorados por terem URLs de imagem inválidas`
      );
    }

    return xml;
  } catch (error) {
    console.error(
      `Erro ao gerar feed do Google Merchant no produto ${currentProduct}, passo "${currentStep}":`,
      error
    );
    throw error;
  }
}
