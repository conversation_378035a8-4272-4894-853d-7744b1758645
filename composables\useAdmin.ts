import { ref, reactive } from "vue";
import {
  collection,
  getDocs,
  doc,
  updateDoc,
  query,
  limit,
  orderBy,
  startAfter,
  where,
  getDoc,
} from "firebase/firestore";
import type { Figure, FormattedFigure } from "~/types/figures";
import { formatFigureData } from "~/types/figures";
import { useNuxtApp } from "#app";
import { useFetch } from "#app";
import {
  FILTER_WORDS_TO_REMOVE,
  FILTER_REGEX_PATTERNS,
} from "~/config/constants";

export const useAdmin = () => {
  const { $db } = useNuxtApp();
  console.log("🔥 Firestore DB:", $db ? "Conectado" : "Não conectado");

  // Loading states
  const loading = reactive({
    fetchFigures: false,
    fetchTotal: false,
    updateFigure: false,
    updateAll: false,
  });

  const error = ref<string | null>(null);
  const totalItems = ref(0);
  const PAGE_SIZE = 10;

  // Nome da coleção no Firebase
  const COLLECTION_NAME = "products";

  // Gerar tokens de busca para um título
  const generateSearchTokens = (title: string): string[] => {
    if (!title) return [];

    const tokens = new Set<string>();
    const normalizedTitle = title.toLowerCase().trim();

    // Adiciona o título completo após remover palavras filtradas
    let processedTitle = normalizedTitle;

    // Remove padrões de regex primeiro
    FILTER_REGEX_PATTERNS.forEach((pattern) => {
      processedTitle = processedTitle.replace(pattern, "");
    });

    // Remove palavras específicas
    FILTER_WORDS_TO_REMOVE.forEach((word) => {
      processedTitle = processedTitle.replace(
        new RegExp(`\\b${word}\\b`, "gi"),
        ""
      );
    });

    processedTitle = processedTitle.trim();

    // Adiciona cada palavra individual, excluindo as palavras filtradas
    const words = processedTitle.split(/\s+/);
    words.forEach((word) => {
      const normalizedWord = word.toLowerCase();
      if (
        word.length >= 2 &&
        !FILTER_WORDS_TO_REMOVE.includes(normalizedWord) &&
        !FILTER_REGEX_PATTERNS.some((pattern) => pattern.test(normalizedWord))
      ) {
        tokens.add(normalizedWord);
      }
    });

    return Array.from(tokens);
  };

  // Remover campos undefined de um objeto
  const removeUndefinedFields = (obj: any) => {
    const clean: any = {};
    Object.entries(obj).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        clean[key] = value;
      }
    });
    return clean;
  };

  // Buscar figures com paginação
  const fetchFigures = async (
    page = 1,
    searchTerm = "",
    categoryFilter = { dragonBall: false }
  ) => {
    console.log("📥 Iniciando fetchFigures:", {
      page,
      searchTerm,
      categoryFilter,
    });
    if (loading.fetchFigures) {
      console.log("⚠️ Já existe uma busca em andamento");
      return [];
    }

    loading.fetchFigures = true;
    error.value = null;

    try {
      const productsRef = collection($db, COLLECTION_NAME);
      let constraints: QueryConstraint[] = [];

      // Ordenação sempre presente
      constraints.push(orderBy("title"));

      // Filtro de categoria
      if (categoryFilter.dragonBall) {
        constraints.push(where("categories", "array-contains", "Dragon Ball"));
      }

      // Busca por tokens
      if (searchTerm) {
        const searchTokens = generateSearchTokens(searchTerm);
        if (searchTokens.length > 0) {
          // Busca por qualquer token que corresponda
          constraints.push(
            where("search_tokens", "array-contains-any", searchTokens)
          );
        }
      }

      // Paginação
      const skipDocs = (page - 1) * PAGE_SIZE;
      if (skipDocs > 0) {
        const previousPageQuery = query(
          productsRef,
          ...constraints,
          limit(skipDocs)
        );
        const previousSnapshot = await getDocs(previousPageQuery);

        if (previousSnapshot.docs.length > 0) {
          const lastDoc =
            previousSnapshot.docs[previousSnapshot.docs.length - 1];
          constraints.push(startAfter(lastDoc));
        }
      }

      constraints.push(limit(PAGE_SIZE));

      const finalQuery = query(productsRef, ...constraints);
      const snapshot = await getDocs(finalQuery);

      console.log("📦 Documentos encontrados:", snapshot.size);
      const figures = snapshot.docs.map((doc) => {
        const data = doc.data() as Figure;
        return formatFigureData(data, doc.id);
      });

      return figures;
    } catch (e) {
      console.error("❌ Erro ao buscar figures:", e);
      error.value = "Erro ao carregar as figures. Por favor, tente novamente.";
      return [];
    } finally {
      loading.fetchFigures = false;
    }
  };

  // Buscar total de items
  const fetchTotalItems = async (
    searchTerm = "",
    categoryFilter = { dragonBall: false }
  ) => {
    console.log("📊 Buscando total de items:", { searchTerm, categoryFilter });
    loading.fetchTotal = true;
    try {
      const productsRef = collection($db, COLLECTION_NAME);
      const snapshot = await getDocs(query(productsRef));

      let total = snapshot.size;

      // Aplicar filtros no cliente para contar
      const figures = snapshot.docs.map((doc) => doc.data() as Figure);

      if (searchTerm) {
        const searchTokens = generateSearchTokens(searchTerm);
        total = figures.filter((figure) =>
          figure.search_tokens?.some((token) => searchTokens.includes(token))
        ).length;
      }

      if (categoryFilter.dragonBall) {
        total = figures.filter((figure) =>
          figure.categories?.includes("Dragon Ball")
        ).length;
      }

      console.log("📊 Total de items encontrados:", total);
      totalItems.value = total;
      return total;
    } catch (e) {
      console.error("❌ Erro ao contar figures:", e);
      error.value = "Erro ao contar figures. Por favor, tente novamente.";
      return 0;
    } finally {
      loading.fetchTotal = false;
    }
  };

  // Atualizar figure com tokens de busca
  const updateFigure = async (
    figureId: string,
    updates: Partial<Figure>
  ): Promise<boolean> => {
    console.log("📝 Iniciando atualização da figure:", { figureId, updates });
    loading.updateFigure = true;
    error.value = null;
    try {
      const figureRef = doc($db, COLLECTION_NAME, figureId);
      const currentFigure = (await getDoc(figureRef)).data() as Figure;

      // Gerar tokens de busca se o título foi atualizado
      if (updates.title && updates.title !== currentFigure.title) {
        const searchTokens = generateSearchTokens(updates.title);
        // Manter os tokens existentes e adicionar os novos do título
        updates.search_tokens = [
          ...new Set([...(updates.search_tokens || []), ...searchTokens]),
        ];
      }



      // Remover campos undefined
      const cleanUpdates = Object.fromEntries(
        Object.entries(updates).filter(([_, value]) => value !== undefined && value !== null)
      );

      console.log(
        "📝 Dados que serão salvos no Firebase:",
        JSON.stringify(cleanUpdates, null, 2)
      );

      await updateDoc(figureRef, cleanUpdates);
      console.log("✅ Figure atualizada com sucesso");

      // Verificar se os dados foram salvos corretamente
      const updatedDoc = await getDoc(figureRef);
      console.log(
        "🔍 Dados após salvamento:",
        JSON.stringify(updatedDoc.data(), null, 2)
      );

      return true;
    } catch (e) {
      console.error("❌ Erro ao atualizar figure:", e);
      error.value = "Erro ao atualizar a figure. Por favor, tente novamente.";
      return false;
    } finally {
      loading.updateFigure = false;
    }
  };

  // Atualizar todas as figures com tokens de busca
  const updateAllFiguresWithSearchTokens = async () => {
    console.log("🔄 Iniciando verificação de figures via API...");
    loading.updateAll = true;
    error.value = null;

    try {
      // Chamar o endpoint do servidor para atualizar os tokens
      const { data, error: apiError } = await useFetch('/api/admin/update-tokens', {
        method: 'POST'
      });

      if (apiError.value) {
        throw new Error(apiError.value.message || 'Erro ao atualizar tokens');
      }

      console.log("✅ Resposta da API:", data.value);

      return data.value;
    } catch (e) {
      console.error("❌ Erro ao atualizar figures:", e);
      error.value = "Erro ao atualizar as figures. Por favor, tente novamente.";
      return {
        success: false,
        message: "Ocorreu um erro ao atualizar as figures.",
        error: e,
      };
    } finally {
      loading.updateAll = false;
    }
  };

  // Busca figures para herdar (sem paginação)
  const searchFiguresForInherit = async (searchTerm: string, currentFigureId?: string) => {
    const figuresRef = collection($db, 'products');
    let q;

    if (searchTerm) {
      // Busca por tokens
      q = query(
        figuresRef,
        where('search_tokens', 'array-contains', searchTerm.toLowerCase()),
        limit(5)
      );
    } else {
      // Sem termo de busca, retorna as mais recentes
      q = query(
        figuresRef,
        orderBy('created_at', 'desc'),
        limit(5)
      );
    }

    const snapshot = await getDocs(q);
    return snapshot.docs
      .filter(doc => doc.id !== currentFigureId)
      .map(doc => ({
        id: doc.id,
        ...doc.data(),
        formattedPrice: new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL'
        }).format(doc.data().base_price)
      }));
  };

  return {
    loading,
    error,
    totalItems,
    PAGE_SIZE,
    fetchFigures,
    fetchTotalItems,
    updateFigure,
    updateAllFiguresWithSearchTokens,
    searchFiguresForInherit,
  };
};
