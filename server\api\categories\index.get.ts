import { createError } from "h3";
import { collection, getDocs } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { Category, formatCategoryData } from "~/types/categories";

// Cache em memória para categorias
const CACHE_EXPIRATION_TIME = 60 * 60 * 1000; // 1 hora em milissegundos
let categoriesCache = {
  data: null,
  timestamp: 0
};

export default defineEventHandler(async (event) => {
  try {
    // Obter parâmetros de consulta
    const query = getQuery(event);
    const forceRefresh = query.refresh === 'true';

    // Verificar se podemos usar o cache
    const isCacheValid =
      !forceRefresh &&
      categoriesCache.data !== null &&
      (Date.now() - categoriesCache.timestamp) < CACHE_EXPIRATION_TIME;

    let categories = [];

    // Se o cache for válido, use o cache
    if (isCacheValid) {
      console.log('🔍 [API] Usando cache de categorias');
      categories = categoriesCache.data;
    } else {
      console.log("🔍 [API] Buscando todas as categorias do Firebase");

      // Buscar todas as categorias
      const categoriesRef = collection(db, "categories");
      const querySnapshot = await getDocs(categoriesRef);

      categories = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data() as { name: string; fatherId?: string }
      }));

      // Atualizar o cache
      categoriesCache.data = categories;
      categoriesCache.timestamp = Date.now();
      console.log('🔍 [API] Cache de categorias atualizado');
    }

    return categories;
  } catch (error) {
    console.error("Erro ao buscar categorias:", error);
    throw createError({
      statusCode: 500,
      message: "Erro ao buscar categorias"
    });
  }
});
