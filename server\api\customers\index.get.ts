import { collection, getDocs } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { createError } from "h3";

const USERS_COLLECTION = "users";

// Cache em memória para clientes
const CACHE_EXPIRATION_TIME = 60 * 60 * 1000; // 1 hora em milissegundos
let customersCache = {
  data: null,
  timestamp: 0
};

/**
 * Rota para listar todos os clientes
 * GET /api/customers
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter parâmetros de consulta
    const query = getQuery(event);
    const forceRefresh = query.refresh === 'true';

    // Verificar se podemos usar o cache
    const isCacheValid =
      !forceRefresh &&
      customersCache.data !== null &&
      (Date.now() - customersCache.timestamp) < CACHE_EXPIRATION_TIME;

    let customers = [];

    // Se o cache for válido, use o cache
    if (isCacheValid) {
      console.log('🔍 [API] Usando cache de clientes');
      customers = customersCache.data;
    } else {
      console.log("🔍 [API] Buscando todos os clientes do Firebase");

      // Buscar todos os clientes
      const customersRef = collection(db, USERS_COLLECTION);
      const customersSnapshot = await getDocs(customersRef);

      // Formatar os clientes
      customers = customersSnapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
        };
      });

      // Atualizar o cache
      customersCache.data = customers;
      customersCache.timestamp = Date.now();
      console.log('🔍 [API] Cache de clientes atualizado');
    }

    console.log(`✅ [API] ${customers.length} clientes encontrados`);

    return { customers };
  } catch (error: any) {
    console.error("❌ [API] Erro ao buscar clientes:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "Erro ao buscar clientes",
    });
  }
});
